import { Fragment, useEffect, useState } from 'react';
import { Card, Col, Row, Button, Form, Alert } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import Pageheader from 'layout/layoutcomponent/pageheader.jsx';
import useGeneralStore from 'store/generalStore.js';
import DossierService from 'service/api/dossierService.js';
import toastService from 'utils/toastService.js';
import Loader from 'layout/layoutcomponent/loaders';
import { formatJalaliDate } from 'utils/helper.js';
import DatePicker from 'react-multi-date-picker';
import persian from 'react-date-object/calendars/persian';
import persian_fa from 'react-date-object/locales/persian_fa';
import { formatDate } from '../../utils/helper';
import { getFormOptions } from '../../utils/formOptions';
import Select from 'react-select';

const RulingIssued = () => {
  const { uuid } = useParams();
  const navigate = useNavigate();
  const { setData } = useGeneralStore();

  // Get authority options from formOptions
  const authorities = getFormOptions('authorities');

  const [dossier, setDossier] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    rulingDate: '',
    rulingAuthority: {},
    rulingDocument: null,
    researchDetails: {
      researchType: '',
      bibliographicInfo: '',
      hasPublishedArticle: null,
      articleCount: 1,
      articleType: '',
      hasRetraction: null,
      journalLanguage: '',
      indexingDatabases: [],
    },
  });

  // Research types
  const researchTypes = [
    'پروپوزال و پیشنهاده پژوهش',
    'پایان‌نامه',
    'طرح پژوهشی',
    'مقاله علمی در مجله',
    'مقاله کنگره و سمینار',
    'پوستر',
    'سخنرانی',
    'پروانه ثبت اختراع',
    'کتاب',
  ];

  // Article types
  const articleTypes = ['اصیل', 'مروری', 'گزارش مورد', 'گزارش کوتاه', 'سایر'];

  // Fetch dossier data
  useEffect(() => {
    const fetchDossierData = async () => {
      if (!uuid) {
        toastService.error('شناسه پرونده معتبر نیست');
        navigate('/app/inquiry');
        return;
      }

      try {
        setLoading(true);
        const response = await DossierService.getDossier(uuid);
        const dossierData = response?.data?.data;

        if (dossierData) {
          setDossier(dossierData);
          setData({ pageTitle: `صدور حکم/رای مراجع ذیصلاح - پرونده ${dossierData.dossier_code || ''}` });

          // Check if dossier is in correct status
          if (dossierData.dossier_status !== 'ruling_issued') {
            toastService.error('این پرونده در مرحله صدور حکم/رای نیست');
            navigate('/app/inquiry');
            return;
          }

          // Load existing data if available
          if (dossierData.ruling_issued) {
            setFormData({
              rulingDate: dossierData.ruling_issued.rulingDate || '',
              rulingAuthority: dossierData.ruling_issued.rulingAuthority || '',
              rulingDocument: dossierData.ruling_issued.rulingDocument || null,
              // researchDetails: {
              //   ...formData.researchDetails,
              //   ...dossierData.ruling_issued.researchDetails,
              // },
            });
          }
        } else {
          throw new Error('Dossier data not found');
        }
      } catch (error) {
        console.error('Error fetching dossier:', error);
        toastService.error('خطا در دریافت اطلاعات پرونده');
        navigate('/app/inquiry');
      } finally {
        setLoading(false);
      }
    };

    fetchDossierData();
  }, [uuid, setData, navigate]);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle research details changes
  const handleResearchDetailChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      researchDetails: { ...prev.researchDetails, [field]: value },
    }));
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setSubmitting(true);
      const formDataUpload = new FormData();
      formDataUpload.append('document', file);
      formDataUpload.append('document_source', 'ruling_issued');
      formDataUpload.append('dossier', uuid);

      const response = await DossierService.uploadDocument(formDataUpload);

      handleInputChange('rulingDocument', response.data);
      toastService.success('فایل با موفقیت آپلود شد');
    } catch (error) {
      console.error('Error uploading file:', error);
      toastService.error('خطا در آپلود فایل');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle file removal
  const handleFileRemove = () => {
    handleInputChange('rulingDocument', null);
    toastService.success('فایل حذف شد');
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (
      !formData.rulingDate ||
      !formData.rulingAuthority
      // !formData.researchDetails.researchType
      // !formData.researchDetails.bibliographicInfo
    ) {
      toastService.error('لطفاً تمام فیلدهای الزامی را تکمیل کنید');
      return;
    }

    if (!formData.rulingDocument) {
      toastService.error('بارگذاری مستندات حکم الزامی است');
      return;
    }

    try {
      setSubmitting(true);

      // Submit ruling data
      const rulingData = {
        ruling_issued: {
          session_date: formData.rulingDate,
          reviewing_authority: formData.rulingAuthority.value || '---',
        },
      };

      const response = await DossierService.updateRulingIssuedDossier(uuid, rulingData);

      if (response?.data?.status === 'OK') {
        await DossierService.submitDossier(uuid, { description: '---' });
        toastService.success('صدور حکم/رای مراجع رسیدگی کننده با موفقیت ثبت شد و پرونده تکمیل گردید');
        navigate('/app/inquiry');
      }
    } catch (error) {
      console.error('Error submitting ruling:', error);
      toastService.error('خطا در ثبت حکم/رای');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (!dossier) {
    return (
      <Fragment>
        <Pageheader title="خطا" />
        <Row>
          <Col lg={12}>
            <Card>
              <Card.Body className="text-center">
                <h4>پرونده یافت نشد</h4>
                <Button variant="primary" onClick={() => navigate('/app/inquiry')}>
                  بازگشت به لیست پرونده‌ها
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Fragment>
    );
  }

  // Check if referral exists
  const referralData = dossier.referred;

  if (!referralData) {
    return (
      <Fragment>
        <Pageheader title="خطا" />
        <Row>
          <Col lg={12}>
            <Card>
              <Card.Body className="text-center">
                <Alert variant="info">
                  <i className="fe fe-info me-2"></i>
                  این بخش فقط در صورت ارجاع به مراجع ذیصلاح نمایش داده می‌شود. از آنجا که پرونده ارجاع نشده است، این
                  مرحله نادیده گرفته می‌شود.
                </Alert>
                <Button variant="primary" onClick={() => navigate('/app/inquiry')}>
                  بازگشت به لیست پرونده‌ها
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <Pageheader title={`صدور حکم/رای مراجع ذیصلاح - پرونده ${dossier.dossier_code || ''}`} />

      {/* Action Buttons */}
      <Row className="mb-3">
        <Col lg={12}>
          <div className="d-flex justify-content-between align-items-center">
            <Button variant="outline-secondary" onClick={() => navigate('/app/inquiry')}>
              <i className="fe fe-arrow-right me-2"></i>
              بازگشت به لیست پرونده‌ها
            </Button>

            <Button variant="outline-info" onClick={() => navigate(`/app/dossier/${uuid}/view`)}>
              <i className="fe fe-eye me-2"></i>
              مشاهده کامل پرونده
            </Button>
          </div>
        </Col>
      </Row>

      <Row>
        {/* Dossier Summary */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-info me-2"></i>
                خلاصه پرونده
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>شماره پرونده:</strong> {dossier.dossier_code || '---'}
              </div>
              <div className="mb-3">
                <strong>موسسه:</strong> {dossier.institute.title || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌دهنده:</strong> {dossier.reporter?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌شونده:</strong> {dossier.reportee?.full_name || 'نامشخص'}
              </div>

              {/* Referral Info */}
              <Alert variant="info" className="mt-3">
                <i className="fe fe-info me-2"></i>
                <strong>مرجع ارجاع شده:</strong> {referralData.authority}
                <br />
                <strong>تاریخ ارجاع:</strong> {formatJalaliDate(referralData.referralDate)}
              </Alert>
            </Card.Body>
          </Card>
        </Col>

        {/* Ruling Form */}
        <Col lg={8}>
          <Card>
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-bookmark me-2"></i>
                صدور حکم/رای مراجع رسیدگی کننده
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <Alert variant="success">
                <i className="fe fe-bookmark me-2"></i>
                <strong>صدور حکم/رای مراجع رسیدگی کننده</strong>
                <br />
                در این بخش، حکم یا رای صادر شده از سوی مراجع رسیدگی کننده و مشخصات پژوهش دچار تخلف ثبت می‌شود.
              </Alert>

              <Form onSubmit={handleSubmit}>
                {/* Ruling Information */}
                <Card className="mb-4">
                  <Card.Header>
                    <Card.Title className="mb-0">اطلاعات حکم/رای</Card.Title>
                  </Card.Header>
                  <Card.Body>
                    <Row className="mb-3">
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>تاریخ صدور حکم:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <DatePicker
                            value={formData.rulingDate ? new Date(formData.rulingDate) : null}
                            calendar={persian}
                            locale={persian_fa}
                            calendarPosition="bottom-right"
                            containerClassName={'form-control'}
                            style={{
                              background: 'transparent',
                              width: '100%',
                              boxShadow: 'none!important',
                              outline: 'none',
                              border: 'none',
                            }}
                            onChange={(date) => {
                              const formattedDate = date ? formatDate(new Date(date)) : '';
                              handleInputChange('rulingDate', formattedDate);
                            }}
                            placeholder="تاریخ صدور حکم را انتخاب کنید"
                          />
                        </Form.Group>
                      </Col>

                      <Col md={6}>
                        <Form.Group className="control-group form-group">
                          <Form.Label className="form-label">
                            <strong>مرجع صادر کننده حکم:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Select
                            options={authorities.map((authority) => ({
                              value: authority.value,
                              label: authority.label,
                            }))}
                            placeholder={'مرجع صادر کننده حکم را انتخاب کنید'}
                            classNamePrefix="Select2"
                            isSearchable
                            // value={{ value: formData.rulingAuthority.value, label: formData.rulingAuthority.label }}
                            onChange={(e) => handleInputChange('rulingAuthority', e)}
                            required
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    <Row className="mb-3">
                      <Col md={12}>
                        <Form.Group>
                          <Form.Label>
                            <strong>بارگذاری مستندات حکم:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Form.Control
                            type="file"
                            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                            onChange={handleFileUpload}
                            required={!formData.rulingDocument}
                            disabled={submitting}
                          />
                          <Form.Text className="text-muted">فرمت‌های مجاز: PDF, DOC, DOCX, JPG, PNG</Form.Text>

                          {formData.rulingDocument && (
                            <div className="mt-2">
                              <Alert
                                variant="success"
                                className="py-2 d-flex justify-content-between align-items-center"
                              >
                                <div>
                                  <i className="fe fe-file me-2"></i>
                                  فایل حکم با موفقیت آپلود شد: {formData.rulingDocument.name || 'فایل آپلود شده'}
                                </div>
                                <Button variant="outline-danger" size="sm" onClick={handleFileRemove}>
                                  <i className="fe fe-trash-2"></i>
                                  حذف
                                </Button>
                              </Alert>
                            </div>
                          )}
                        </Form.Group>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>

                {/* Research Details */}
                {/* <Card className="mb-4">
                  <Card.Header>
                    <Card.Title className="mb-0">مشخصات پژوهش دچار تخلف</Card.Title>
                  </Card.Header>
                  <Card.Body>
                    <Row className="mb-3">
                      <Col md={12}>
                        <Form.Group>
                          <Form.Label>
                            <strong>نوع و قالب تهیه یا انتشار پژوهش:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Form.Select
                            value={formData.researchDetails.researchType}
                            onChange={(e) => handleResearchDetailChange('researchType', e.target.value)}
                            required
                          >
                            <option value="">انتخاب کنید...</option>
                            {researchTypes.map((type, index) => (
                              <option key={index} value={type}>
                                {type}
                              </option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                      </Col>
                    </Row>

                    <Row className="mb-3">
                      <Col md={12}>
                        <Form.Group>
                          <Form.Label>
                            <strong>اطلاعات کتابشناختی پژوهش:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Form.Control
                            as="textarea"
                            rows={4}
                            value={formData.researchDetails.bibliographicInfo}
                            onChange={(e) => handleResearchDetailChange('bibliographicInfo', e.target.value)}
                            placeholder="عنوان، نویسندگان، محل انتشار، سال انتشار و سایر اطلاعات کتابشناختی..."
                            required
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    <Row className="mb-3">
                      <Col md={12}>
                        <Form.Group>
                          <Form.Label>
                            <strong>مقاله/مقاله‌هایی از این پژوهش منتشر شده است:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <div className="mt-2">
                            <Form.Check
                              type="radio"
                              id="has_article"
                              name="hasPublishedArticle"
                              label="بلی"
                              checked={formData.researchDetails.hasPublishedArticle === true}
                              onChange={() => handleResearchDetailChange('hasPublishedArticle', true)}
                              className="mb-2"
                            />

                            <Form.Check
                              type="radio"
                              id="no_article"
                              name="hasPublishedArticle"
                              label="خیر"
                              checked={formData.researchDetails.hasPublishedArticle === false}
                              onChange={() => handleResearchDetailChange('hasPublishedArticle', false)}
                              className="mb-2"
                            />
                          </div>
                        </Form.Group>
                      </Col>
                    </Row>

                    {formData.researchDetails.hasPublishedArticle && (
                      <>
                        <Alert variant="info">
                          <i className="fe fe-info me-2"></i>
                          مقاله‌هایی از این پژوهش منتشر شده است. لطفاً جزئیات را تکمیل کنید.
                        </Alert>

                        <Row className="mb-3">
                          <Col md={6}>
                            <Form.Group>
                              <Form.Label>
                                <strong>تعداد مقاله منتشر شده:</strong>
                                <span className="text-danger ms-1">*</span>
                              </Form.Label>
                              <Form.Select
                                value={formData.researchDetails.articleCount}
                                onChange={(e) => handleResearchDetailChange('articleCount', parseInt(e.target.value))}
                                required
                              >
                                {[...Array(10)].map((_, i) => (
                                  <option key={i + 1} value={i + 1}>
                                    {i + 1}
                                  </option>
                                ))}
                                <option value="more">بیش از 10</option>
                              </Form.Select>
                            </Form.Group>
                          </Col>

                          <Col md={6}>
                            <Form.Group>
                              <Form.Label>
                                <strong>نوع مقاله‌های منتشرشده:</strong>
                                <span className="text-danger ms-1">*</span>
                              </Form.Label>
                              <Form.Select
                                value={formData.researchDetails.articleType}
                                onChange={(e) => handleResearchDetailChange('articleType', e.target.value)}
                                required
                              >
                                <option value="">انتخاب کنید...</option>
                                {articleTypes.map((type, index) => (
                                  <option key={index} value={type}>
                                    {type}
                                  </option>
                                ))}
                              </Form.Select>
                            </Form.Group>
                          </Col>
                        </Row>
                      </>
                    )}
                  </Card.Body>
                </Card> */}

                {/* Submit Button */}
                <div className="d-flex justify-content-end">
                  <Button type="submit" variant="success" disabled={submitting}>
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        در حال ثبت...
                      </>
                    ) : (
                      <>
                        <i className="fe fe-check me-2"></i>
                        ثبت نهایی و تکمیل پرونده
                      </>
                    )}
                  </Button>
                </div>

                {/* Final Process Information */}
                <Alert variant="success" className="mt-3">
                  <i className="fe fe-check me-2"></i>
                  <strong>تکمیل فرآیند:</strong> با تکمیل این بخش، تمام مراحل رسیدگی به پرونده تکمیل شده و پرونده به
                  وضعیت "خاتمه یافته" منتقل خواهد شد.
                </Alert>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default RulingIssued;
