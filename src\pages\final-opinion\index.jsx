import { Fragment, useEffect, useState } from 'react';
import { Card, Col, Row, Button, Form, Alert } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import Select from 'react-select';
import Pageheader from 'layout/layoutcomponent/pageheader.jsx';
import useGeneralStore from 'store/generalStore.js';
import DossierService from 'service/api/dossierService.js';
import toastService from 'utils/toastService.js';
import Loader from 'layout/layoutcomponent/loaders';
import { formatJalaliDate, convertToPersianNumbers } from 'utils/helper.js';
import DatePicker from 'react-multi-date-picker';
import persian from 'react-date-object/calendars/persian';
import persian_fa from 'react-date-object/locales/persian_fa';
import { formatDate } from '../../utils/helper';
import { FORM_OPTIONS, getViolationYears, getViolationCounts } from 'utils/formOptions.js';

const FinalOpinion = () => {
  const { uuid } = useParams();
  const navigate = useNavigate();
  const { setData } = useGeneralStore();

  const [dossier, setDossier] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    meetingDate: '',
    meetingMembers: '',
    statements: '',
    finalOpinion: '',
    opinionDocument: null,

    // فیلدهای احراز تخلف
    violationVerification: '', // احراز یا عدم احراز
    violationYear: '', // سال وقوع تخلف
    violationType: '', // نوع تخلف
    violationTypeOther: '', // سایر (در صورت انتخاب سایر)
    violationCount: '', // تعداد موارد تخلف محرزشده
    benefitFromViolation: '', // انتفاع یا عدم انتفاع
    benefitTypeFaculty: [], // نوع انتفاع هیات علمی
    benefitTypeStudent: [], // نوع انتفاع دانشجو
    benefitTypeEmployee: [], // نوع انتفاع کارمند
    benefitTypeResearcher: [], // نوع انتفاع پژوهشگر آزاد
    benefitOtherDescription: '', // توضیح سایر انتفاع
    violationStage: '', // تخلف در کدام مرحله پژوهش
    violationReasons: '', // دلایل بروز تخلف
    violationPattern: '', // موردی یا سازمان‌یافته
    violationEffects: [], // اثرات تخلف پژوهشی
  });

  // Get options from formOptions
  const verificationOptions = FORM_OPTIONS.violationVerification;
  const violationYears = getViolationYears();
  const violationTypes = FORM_OPTIONS.violationTypes;
  const violationCounts = getViolationCounts();
  const benefitOptions = FORM_OPTIONS.benefitFromViolation;
  const facultyBenefitTypes = FORM_OPTIONS.facultyBenefitTypes;
  const studentBenefitTypes = FORM_OPTIONS.studentBenefitTypes;
  const employeeBenefitTypes = FORM_OPTIONS.employeeBenefitTypes;
  const researcherBenefitTypes = FORM_OPTIONS.researcherBenefitTypes;
  const researchStages = FORM_OPTIONS.violationResearchStages;
  const violationPatterns = FORM_OPTIONS.violationPatterns;
  const violationEffects = FORM_OPTIONS.violationEffects;

  // Handle checkbox changes for arrays
  const handleCheckboxChange = (field, value, checked) => {
    setFormData((prev) => ({
      ...prev,
      [field]: checked ? [...prev[field], value] : prev[field].filter((item) => item !== value),
    }));
  };

  // Fetch dossier data
  useEffect(() => {
    const fetchDossierData = async () => {
      if (!uuid) {
        toastService.error('شناسه پرونده معتبر نیست');
        navigate('/app/inquiry');
        return;
      }

      try {
        setLoading(true);
        const response = await DossierService.getDossier(uuid);
        const dossierData = response?.data?.data;

        if (dossierData) {
          setDossier(dossierData);
          setData({ pageTitle: `نظریه نهایی کارگروه موسسه - پرونده ${dossierData.dossier_code || ''}` });

          // Check if dossier is in correct status
          if (dossierData.dossier_status !== 'final_opinion') {
            toastService.error('این پرونده در مرحله نظریه نهایی نیست');
            navigate('/app/inquiry');
            return;
          }

          // Load existing data if available
          if (dossierData.final_opinion || dossierData.violation_details) {
            setFormData({
              meetingDate: dossierData.final_opinion?.meetingDate || '',
              meetingMembers: dossierData.final_opinion?.meetingMembers || '',
              statements: dossierData.final_opinion?.statements || '',
              finalOpinion: dossierData.final_opinion?.finalOpinion || '',
              opinionDocument: dossierData.final_opinion?.opinionDocument || null,

              // Load violation details data
              violationVerification: dossierData.violation_details?.violation_status || '',
              violationYear: dossierData.violation_details?.violation_year || '',
              violationType: dossierData.violation_details?.violation_type || '',
              violationTypeOther: dossierData.violation_details?.other_violation_type || '',
              violationCount: dossierData.violation_details?.violation_count || '',
              benefitFromViolation: dossierData.violation_details?.benefit_status || '',
              benefitTypeFaculty: dossierData.violation_details?.faculty_benefit
                ? [dossierData.violation_details.faculty_benefit]
                : [],
              benefitTypeStudent: dossierData.violation_details?.student_benefit
                ? [dossierData.violation_details.student_benefit]
                : [],
              benefitTypeEmployee: dossierData.violation_details?.employee_benefit
                ? [dossierData.violation_details.employee_benefit]
                : [],
              benefitTypeResearcher: dossierData.violation_details?.independent_researcher_benefit
                ? [dossierData.violation_details.independent_researcher_benefit]
                : [],
              benefitOtherDescription: dossierData.violation_details?.benefit_other_description || '',
              violationStage: dossierData.violation_details?.research_stage || '',
              violationReasons: dossierData.violation_details?.violation_reasons || '',
              violationPattern: dossierData.violation_details?.violation_pattern || '',
              violationEffects: dossierData.violation_details?.violation_effects || [],
            });
          }
        } else {
          throw new Error('Dossier data not found');
        }
      } catch (error) {
        console.error('Error fetching dossier:', error);
        toastService.error('خطا در دریافت اطلاعات پرونده');
        navigate('/app/inquiry');
      } finally {
        setLoading(false);
      }
    };

    fetchDossierData();
  }, [uuid, setData, navigate]);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setSubmitting(true);
      const formDataUpload = new FormData();
      formDataUpload.append('document', file);
      formDataUpload.append('document_source', 'final_opinion');
      formDataUpload.append('dossier', uuid);

      const response = await DossierService.uploadDocument(formDataUpload);

      handleInputChange('opinionDocument', response.data);
      toastService.success('فایل با موفقیت آپلود شد');
    } catch (error) {
      console.error('Error uploading file:', error);
      toastService.error('خطا در آپلود فایل');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle file removal
  const handleFileRemove = () => {
    handleInputChange('opinionDocument', null);
    toastService.success('فایل حذف شد');
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic validation for final opinion
    if (!formData.meetingDate || !formData.meetingMembers || !formData.statements || !formData.finalOpinion) {
      toastService.error('لطفاً تمام فیلدهای الزامی نظریه نهایی را تکمیل کنید');
      return;
    }

    if (!formData.opinionDocument) {
      toastService.error('بارگذاری صورتجلسه الزامی است');
      return;
    }

    // Validation for violation details
    if (!formData.violationVerification) {
      toastService.error('لطفاً وضعیت احراز تخلف را مشخص کنید');
      return;
    }

    // Additional validation if violation is confirmed
    if (formData.violationVerification === 'confirmed') {
      if (
        !formData.violationYear ||
        !formData.violationType ||
        !formData.violationCount ||
        !formData.benefitFromViolation ||
        !formData.violationStage ||
        !formData.violationReasons ||
        !formData.violationPattern
      ) {
        toastService.error('لطفاً تمام فیلدهای الزامی احراز تخلف را تکمیل کنید');
        return;
      }

      // Validate "other" type description
      if (formData.violationType === 'other' && !formData.violationTypeOther) {
        toastService.error('لطفاً توضیح نوع تخلف "سایر" را وارد کنید');
        return;
      }

      // Validate benefit types if benefit is selected
      if (formData.benefitFromViolation === 'benefited') {
        const hasBenefitType =
          formData.benefitTypeFaculty.length > 0 ||
          formData.benefitTypeStudent.length > 0 ||
          formData.benefitTypeEmployee.length > 0 ||
          formData.benefitTypeResearcher.length > 0;
        if (!hasBenefitType) {
          toastService.error('لطفاً حداقل یک نوع انتفاع را انتخاب کنید');
          return;
        }
      }

      // Validate violation effects
      if (formData.violationEffects.length === 0) {
        toastService.error('لطفاً حداقل یک اثر تخلف پژوهشی را انتخاب کنید');
        return;
      }
    }

    try {
      setSubmitting(true);

      // Submit final opinion data
      const opinionData = {
        final_opinion: {
          session_date: formData.meetingDate,
          session_members: formData.meetingMembers,
          statements: formData.statements,
          opinion: formData.finalOpinion,
        },
      };

      // Submit violation details data
      let violationData = {};
      if (formData.violationVerification === 'confirmed') {
        violationData = {
          violation_details: {
            violation_status: formData.violationVerification,
            violation_year: formData.violationYear || 0,
            violation_type: formData.violationType,
            other_violation_type: formData.violationTypeOther || '',
            violation_count: formData.violationCount || 1,
            benefit_status: formData.benefitFromViolation,
            faculty_benefit: formData.benefitTypeFaculty.length > 0 ? formData.benefitTypeFaculty[0] : '',
            student_benefit: formData.benefitTypeStudent.length > 0 ? formData.benefitTypeStudent[0] : '',
            employee_benefit: formData.benefitTypeEmployee.length > 0 ? formData.benefitTypeEmployee[0] : '',
            independent_researcher_benefit:
              formData.benefitTypeResearcher.length > 0 ? formData.benefitTypeResearcher[0] : '',
            benefit_other_description: formData.benefitOtherDescription || '',
            research_stage: formData.violationStage,
            violation_reasons: formData.violationReasons,
            violation_pattern: formData.violationPattern,
            violation_effects: formData.violationEffects,
          },
        };
      } else {
        violationData = {
          violation_details: {
            violation_status: formData.violationVerification,
          },
        };
      }

      // Submit both final opinion and violation details
      const finalResponse = await DossierService.updateFinalOpponionDossier(uuid, opinionData);
      const violationResponse = await DossierService.updateViolationDetailsDossier(uuid, violationData);

      if (finalResponse?.data?.status === 'OK' && violationResponse?.data?.status === 'OK') {
        await DossierService.submitDossier(uuid, { description: '---' });
        toastService.success('نظریه نهایی کارگروه موسسه و احراز تخلف با موفقیت ثبت شد');
        navigate('/app/inquiry');
      }
    } catch (error) {
      console.error('Error submitting final opinion:', error);
      toastService.error('خطا در ثبت نظریه نهایی');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (!dossier) {
    return (
      <Fragment>
        <Pageheader title="خطا" />
        <Row>
          <Col lg={12}>
            <Card>
              <Card.Body className="text-center">
                <h4>پرونده یافت نشد</h4>
                <Button variant="primary" onClick={() => navigate('/app/inquiry')}>
                  بازگشت به لیست پرونده‌ها
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Fragment>
    );
  }

  // Check if appeal was processed
  const hasAppeal =
    dossier.appeal_request &&
    (dossier.appeal_request.type === 'reportee_appeal' || dossier.appeal_request.type === 'reporter_appeal');

  return (
    <Fragment>
      <Pageheader title={`نظریه نهایی کارگروه موسسه - پرونده ${dossier.dossier_code || ''}`} />

      {/* Action Buttons */}
      <Row className="mb-3">
        <Col lg={12}>
          <div className="d-flex justify-content-between align-items-center">
            <Button variant="outline-secondary" onClick={() => navigate('/app/inquiry')}>
              <i className="fe fe-arrow-right me-2"></i>
              بازگشت به لیست پرونده‌ها
            </Button>

            <Button variant="outline-info" onClick={() => navigate(`/app/dossier/${uuid}/view`)}>
              <i className="fe fe-eye me-2"></i>
              مشاهده کامل پرونده
            </Button>
          </div>
        </Col>
      </Row>

      <Row>
        {/* Dossier Summary */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-info me-2"></i>
                خلاصه پرونده
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>شماره پرونده:</strong> {dossier.dossier_code || '---'}
              </div>
              <div className="mb-3">
                <strong>موسسه:</strong> {dossier.institute.title || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌دهنده:</strong> {dossier.reporter?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌شونده:</strong> {dossier.reportee?.full_name || 'نامشخص'}
              </div>

              {/* Show Ministry Opinion Summary if exists */}
              {hasAppeal && dossier.ministry_review && (
                <Alert variant="info" className="mt-3">
                  <i className="fe fe-award me-2"></i>
                  <strong>نظریه کارگروه وزارتی:</strong>
                  <br />
                  <div className="mt-2 p-2 bg-light rounded">
                    {dossier.ministry_review.opinion.substring(0, 100)}
                    {dossier.ministry_review.opinion.length > 100 && '...'}
                  </div>
                  <small className="text-muted">
                    تاریخ جلسه: {formatJalaliDate(dossier.ministry_review.reviewDate)}
                  </small>
                </Alert>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Final Opinion Form */}
        <Col lg={8}>
          <Card>
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-check-circle me-2"></i>
                نظریه نهایی کارگروه موسسه
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <Alert variant="success">
                <i className="fe fe-info me-2"></i>
                <strong>نظریه نهایی کارگروه موسسه پس از تجدیدنظر</strong>
                <br />
                {hasAppeal ? (
                  <>
                    این نظریه نهایی کارگروه موسسه پس از دریافت نظر کارگروه وزارتی در خصوص درخواست تجدیدنظر صادر می‌شود.
                  </>
                ) : (
                  <>
                    از آنجا که درخواست تجدیدنظری وجود نداشته، این نظریه به عنوان نظریه نهایی کارگروه موسسه محسوب می‌شود.
                  </>
                )}
              </Alert>

              <Form onSubmit={handleSubmit}>
                {/* Meeting Date */}
                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>
                        <strong>تاریخ جلسه:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <DatePicker
                        value={formData.meetingDate ? new Date(formData.meetingDate) : null}
                        calendar={persian}
                        locale={persian_fa}
                        calendarPosition="bottom-right"
                        containerClassName={'form-control'}
                        style={{
                          background: 'transparent',
                          width: '100%',
                          boxShadow: 'none!important',
                          outline: 'none',
                          border: 'none',
                        }}
                        onChange={(date) => {
                          const formattedDate = date ? formatDate(new Date(date)) : '';
                          handleInputChange('meetingDate', formattedDate);
                        }}
                        placeholder="تاریخ جلسه را انتخاب کنید"
                      />

                      <Form.Text className="text-muted">
                        تاریخ برگزاری جلسه کارگروه موسسه برای صدور نظریه نهایی
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Meeting Members */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>اعضای جلسه:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={4}
                        value={formData.meetingMembers}
                        onChange={(e) => handleInputChange('meetingMembers', e.target.value)}
                        placeholder="نام و سمت اعضای حاضر در جلسه کارگروه را وارد کنید..."
                        required
                      />
                      <Form.Text className="text-muted">
                        لطفاً نام کامل و سمت هر یک از اعضای حاضر در جلسه را در خطوط جداگانه وارد کنید.
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Statements */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>اظهارات:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={6}
                        value={formData.statements}
                        onChange={(e) => handleInputChange('statements', e.target.value)}
                        placeholder={
                          hasAppeal
                            ? 'اظهارات و بحث‌های مطرح شده در جلسه با در نظر گیری نظر کارگروه وزارتی را وارد کنید...'
                            : 'اظهارات و بحث‌های مطرح شده در جلسه کارگروه را وارد کنید...'
                        }
                        required
                      />
                      <Form.Text className="text-muted">
                        {hasAppeal ? (
                          <>خلاصه‌ای از بحث‌ها و نظرات مطرح شده با در نظر گیری نظریه کارگروه وزارتی.</>
                        ) : (
                          <>خلاصه‌ای از بحث‌ها، نظرات و اظهارات مطرح شده در جلسه کارگروه.</>
                        )}
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Final Opinion */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>نظریه نهایی:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={8}
                        value={formData.finalOpinion}
                        onChange={(e) => handleInputChange('finalOpinion', e.target.value)}
                        placeholder={
                          hasAppeal
                            ? 'نظریه نهایی کارگروه موسسه با در نظر گیری نظر کارگروه وزارتی را وارد کنید...'
                            : 'نظریه نهایی کارگروه موسسه را وارد کنید...'
                        }
                        required
                      />
                      <Form.Text className="text-muted">
                        {hasAppeal ? (
                          <>نظریه و تصمیم نهایی کارگروه موسسه پس از بررسی نظر کارگروه وزارتی.</>
                        ) : (
                          <>نظریه و تصمیم نهایی کارگروه موسسه در خصوص پرونده مورد بررسی.</>
                        )}
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Opinion Document Upload */}
                <Row className="mb-4">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>بارگذاری صورتجلسه:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        type="file"
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        onChange={handleFileUpload}
                        required={!formData.opinionDocument}
                        disabled={submitting}
                      />
                      <Form.Text className="text-muted">فرمت‌های مجاز: PDF, DOC, DOCX, JPG, PNG</Form.Text>

                      {formData.opinionDocument && (
                        <div className="mt-2">
                          <Alert variant="success" className="py-2 d-flex justify-content-between align-items-center">
                            <div>
                              <i className="fe fe-file me-2"></i>
                              فایل صورتجلسه با موفقیت آپلود شد: {formData.opinionDocument.name || 'فایل آپلود شده'}
                            </div>
                            <Button variant="outline-danger" size="sm" onClick={handleFileRemove}>
                              <i className="fe fe-trash-2"></i>
                              حذف
                            </Button>
                          </Alert>
                        </div>
                      )}
                    </Form.Group>
                  </Col>
                </Row>

                {/* Violation Details Section */}
                <hr className="my-4" />
                <h5 className="mb-3">
                  <i className="fe fe-alert-triangle me-2"></i>
                  احراز تخلف
                </h5>

                {/* Violation Verification */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>وضعیت احراز تخلف:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Select
                        options={verificationOptions}
                        placeholder="وضعیت احراز تخلف را انتخاب کنید"
                        classNamePrefix="Select2"
                        value={verificationOptions.find((option) => option.value === formData.violationVerification)}
                        onChange={(e) => handleInputChange('violationVerification', e.value)}
                        isSearchable
                      />
                      <Form.Text className="text-muted">
                        بر اساس بررسی‌های انجام شده، وضعیت احراز تخلف را مشخص کنید.
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Conditional fields for confirmed violation */}
                {formData.violationVerification === 'confirmed' && (
                  <>
                    {/* Violation Year and Type */}
                    <Row className="mb-3">
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>سال وقوع تخلف:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Select
                            options={violationYears}
                            placeholder="سال وقوع تخلف را انتخاب کنید"
                            classNamePrefix="Select2"
                            value={violationYears.find((option) => option.value === formData.violationYear)}
                            onChange={(e) => handleInputChange('violationYear', e.value)}
                            isSearchable
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>نوع تخلف:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Select
                            options={violationTypes}
                            placeholder="نوع تخلف را انتخاب کنید"
                            classNamePrefix="Select2"
                            value={violationTypes.find((option) => option.value === formData.violationType)}
                            onChange={(e) => handleInputChange('violationType', e.value)}
                            isSearchable
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* Other violation type description */}
                    {formData.violationType === 'other' && (
                      <Row className="mb-3">
                        <Col md={12}>
                          <Form.Group>
                            <Form.Label>
                              <strong>توضیح نوع تخلف (سایر):</strong>
                              <span className="text-danger ms-1">*</span>
                            </Form.Label>
                            <Form.Control
                              as="textarea"
                              rows={3}
                              value={formData.violationTypeOther}
                              onChange={(e) => handleInputChange('violationTypeOther', e.target.value)}
                              placeholder="نوع تخلف را به تفصیل شرح دهید..."
                            />
                          </Form.Group>
                        </Col>
                      </Row>
                    )}

                    {/* Violation Count and Benefit */}
                    <Row className="mb-3">
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>تعداد موارد تخلف محرزشده:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Select
                            options={violationCounts}
                            placeholder="تعداد موارد تخلف را انتخاب کنید"
                            classNamePrefix="Select2"
                            value={violationCounts.find((option) => option.value === formData.violationCount)}
                            onChange={(e) => handleInputChange('violationCount', e.value)}
                            isSearchable
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>انتفاع گزارش شونده از تخلف:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <div className="mt-2">
                            {benefitOptions.map((option) => (
                              <Form.Check
                                key={option.value}
                                type="radio"
                                id={`benefit-${option.value}`}
                                name="benefitFromViolation"
                                label={option.label}
                                value={option.value}
                                checked={formData.benefitFromViolation === option.value}
                                onChange={(e) => handleInputChange('benefitFromViolation', e.target.value)}
                                className="mb-2"
                              />
                            ))}
                          </div>
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* Benefit Types - Show only if benefited */}
                    {formData.benefitFromViolation === 'benefited' && (
                      <>
                        {/* Faculty Benefit Types */}
                        <Row className="mb-3">
                          <Col md={12}>
                            <Form.Group>
                              <Form.Label>
                                <strong>نوع انتفاع هیات علمی:</strong>
                              </Form.Label>
                              <div className="mt-2">
                                {facultyBenefitTypes.map((option) => (
                                  <Form.Check
                                    key={option.value}
                                    type="checkbox"
                                    id={`faculty-benefit-${option.value}`}
                                    label={option.label}
                                    checked={formData.benefitTypeFaculty.includes(option.value)}
                                    onChange={(e) =>
                                      handleCheckboxChange('benefitTypeFaculty', option.value, e.target.checked)
                                    }
                                    className="mb-2"
                                  />
                                ))}
                              </div>
                            </Form.Group>
                          </Col>
                        </Row>

                        {/* Student Benefit Types */}
                        <Row className="mb-3">
                          <Col md={12}>
                            <Form.Group>
                              <Form.Label>
                                <strong>نوع انتفاع دانشجو:</strong>
                              </Form.Label>
                              <div className="mt-2">
                                {studentBenefitTypes.map((option) => (
                                  <Form.Check
                                    key={option.value}
                                    type="checkbox"
                                    id={`student-benefit-${option.value}`}
                                    label={option.label}
                                    checked={formData.benefitTypeStudent.includes(option.value)}
                                    onChange={(e) =>
                                      handleCheckboxChange('benefitTypeStudent', option.value, e.target.checked)
                                    }
                                    className="mb-2"
                                  />
                                ))}
                              </div>
                            </Form.Group>
                          </Col>
                        </Row>
                      </>
                    )}

                    {/* Research Stage and Reasons */}
                    <Row className="mb-3">
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>تخلف در کدام مرحله پژوهش:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Select
                            options={researchStages}
                            placeholder="مرحله پژوهش را انتخاب کنید"
                            classNamePrefix="Select2"
                            value={researchStages.find((option) => option.value === formData.violationStage)}
                            onChange={(e) => handleInputChange('violationStage', e.value)}
                            isSearchable
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>موردی یا سازمان‌یافته:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Select
                            options={violationPatterns}
                            placeholder="الگوی تخلف را انتخاب کنید"
                            classNamePrefix="Select2"
                            value={violationPatterns.find((option) => option.value === formData.violationPattern)}
                            onChange={(e) => handleInputChange('violationPattern', e.value)}
                            isSearchable
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* Violation Reasons */}
                    <Row className="mb-3">
                      <Col md={12}>
                        <Form.Group>
                          <Form.Label>
                            <strong>دلایل بروز تخلف:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Form.Control
                            as="textarea"
                            rows={4}
                            value={formData.violationReasons}
                            onChange={(e) => handleInputChange('violationReasons', e.target.value)}
                            placeholder="دلایل و عوامل مؤثر در بروز تخلف را شرح دهید..."
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* Violation Effects */}
                    <Row className="mb-3">
                      <Col md={12}>
                        <Form.Group>
                          <Form.Label>
                            <strong>اثرات تخلف پژوهشی:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <div className="mt-2">
                            {violationEffects.map((option) => (
                              <Form.Check
                                key={option.value}
                                type="checkbox"
                                id={`effect-${option.value}`}
                                label={option.label}
                                checked={formData.violationEffects.includes(option.value)}
                                onChange={(e) =>
                                  handleCheckboxChange('violationEffects', option.value, e.target.checked)
                                }
                                className="mb-2"
                              />
                            ))}
                          </div>
                        </Form.Group>
                      </Col>
                    </Row>
                  </>
                )}

                {/* Submit Button */}
                <div className="d-flex justify-content-end">
                  <Button type="submit" variant="success" disabled={submitting}>
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        در حال ثبت...
                      </>
                    ) : (
                      <>
                        <i className="fe fe-check me-2"></i>
                        ثبت نظریه نهایی و احراز تخلف
                      </>
                    )}
                  </Button>
                </div>

                {/* Process Information */}
                <Alert variant="info" className="mt-3">
                  <i className="fe fe-info me-2"></i>
                  <strong>مرحله بعدی:</strong> پس از تکمیل نظریه نهایی و احراز تخلف، در صورت لزوم پرونده به مراجع رسیدگی
                  کننده ارجاع خواهد شد.
                </Alert>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default FinalOpinion;
