import { Fragment, useEffect, useState } from 'react';
import { Card, Col, Row, Button, Form, Alert } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import Pageheader from 'layout/layoutcomponent/pageheader.jsx';
import useGeneralStore from 'store/generalStore.js';
import DossierService from 'service/api/dossierService.js';
import toastService from 'utils/toastService.js';
import Loader from 'layout/layoutcomponent/loaders';
import { formatJalaliDate } from 'utils/helper.js';
import DatePicker from 'react-multi-date-picker';
import persian from 'react-date-object/calendars/persian';
import persian_fa from 'react-date-object/locales/persian_fa';
import { formatDate } from '../../utils/helper';

const MinistryOpinion = () => {
  const { uuid } = useParams();
  const navigate = useNavigate();
  const { setData } = useGeneralStore();

  const [dossier, setDossier] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    reviewDate: '',
    statements: '',
    opinion: '',
    opinionDocument: null,
  });

  // Fetch dossier data
  useEffect(() => {
    const fetchDossierData = async () => {
      if (!uuid) {
        toastService.error('شناسه پرونده معتبر نیست');
        navigate('/app/inquiry');
        return;
      }

      try {
        setLoading(true);
        const response = await DossierService.getDossier(uuid);
        const dossierData = response?.data?.data;

        if (dossierData) {
          setDossier(dossierData);
          setData({ pageTitle: `صدور نظریه کارگروه وزارتی - پرونده ${dossierData.dossier_code || ''}` });

          // Check if dossier is in correct status
          if (dossierData.dossier_status !== 'ministry_opinion') {
            toastService.error('این پرونده در مرحله صدور نظریه کارگروه وزارتی نیست');
            navigate('/app/inquiry');
            return;
          }

          // Load existing data if available
          if (dossierData.ministry_opinion) {
            setFormData({
              reviewDate: dossierData.ministry_opinion.reviewDate || '',
              statements: dossierData.ministry_opinion.statements || '',
              opinion: dossierData.ministry_opinion.opinion || '',
              opinionDocument: dossierData.ministry_opinion.opinionDocument || null,
            });
          }
        } else {
          throw new Error('Dossier data not found');
        }
      } catch (error) {
        console.error('Error fetching dossier:', error);
        toastService.error('خطا در دریافت اطلاعات پرونده');
        navigate('/app/inquiry');
      } finally {
        setLoading(false);
      }
    };

    fetchDossierData();
  }, [uuid, setData, navigate]);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setSubmitting(true);
      const formDataUpload = new FormData();
      formDataUpload.append('document', file);
      formDataUpload.append('document_source', 'ministry_opinion');
      formDataUpload.append('dossier', uuid);

      const response = await DossierService.uploadDocument(formDataUpload);

      handleInputChange('opinionDocument', response.data);
      toastService.success('فایل با موفقیت آپلود شد');
    } catch (error) {
      console.error('Error uploading file:', error);
      toastService.error('خطا در آپلود فایل');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle file removal
  const handleFileRemove = () => {
    handleInputChange('opinionDocument', null);
    toastService.success('فایل حذف شد');
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.reviewDate || !formData.statements || !formData.opinion) {
      toastService.error('لطفاً تمام فیلدهای الزامی را تکمیل کنید');
      return;
    }

    if (!formData.opinionDocument) {
      toastService.error('بارگذاری صورتجلسه الزامی است');
      return;
    }

    try {
      setSubmitting(true);

      // Submit ministry opinion data
      const opinionData = {
        ministry_opinion: {
          session_date: formData.reviewDate,
          statements: formData.statements,
          opinion: formData.opinion,
        },
      };

      // Note: Using generic updateDossier since no specific ministry opinion service method exists
      const response = await DossierService.updateMinistryOpinionDossier(uuid, opinionData);

      if (response?.data?.status === 'OK') {
        await DossierService.submitDossier(uuid, { description: '---' });
        toastService.success('نظریه کارگروه وزارتی با موفقیت ثبت شد');
        navigate('/app/inquiry');
      }
    } catch (error) {
      console.error('Error submitting ministry opinion:', error);
      toastService.error('خطا در ثبت نظریه کارگروه وزارتی');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (!dossier) {
    return (
      <Fragment>
        <Pageheader title="خطا" />
        <Row>
          <Col lg={12}>
            <Card>
              <Card.Body className="text-center">
                <h4>پرونده یافت نشد</h4>
                <Button variant="primary" onClick={() => navigate('/app/inquiry')}>
                  بازگشت به لیست پرونده‌ها
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Fragment>
    );
  }

  // Check if appeal request exists
  const hasAppeal =
    dossier.revision_status &&
    (dossier.revision_status === 'reportee_review' || dossier.revision_status === 'reporter_review');

  if (!hasAppeal) {
    return (
      <Fragment>
        <Pageheader title="خطا" />
        <Row>
          <Col lg={12}>
            <Card>
              <Card.Body className="text-center">
                <Alert variant="info">
                  <i className="fe fe-info me-2"></i>
                  این بخش فقط در صورت وجود درخواست تجدیدنظر نمایش داده می‌شود. از آنجا که درخواست تجدیدنظری ثبت نشده
                  است، این مرحله نادیده گرفته می‌شود.
                </Alert>
                <Button variant="primary" onClick={() => navigate('/app/inquiry')}>
                  بازگشت به لیست پرونده‌ها
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <Pageheader title={`صدور نظریه کارگروه وزارتی - پرونده ${dossier.dossier_code || ''}`} />

      {/* Action Buttons */}
      <Row className="mb-3">
        <Col lg={12}>
          <div className="d-flex justify-content-between align-items-center">
            <Button variant="outline-secondary" onClick={() => navigate('/app/inquiry')}>
              <i className="fe fe-arrow-right me-2"></i>
              بازگشت به لیست پرونده‌ها
            </Button>

            <Button variant="outline-info" onClick={() => navigate(`/app/dossier/${uuid}/view`)}>
              <i className="fe fe-eye me-2"></i>
              مشاهده کامل پرونده
            </Button>
          </div>
        </Col>
      </Row>

      <Row>
        {/* Dossier Summary */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-info me-2"></i>
                خلاصه پرونده
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>شماره پرونده:</strong> {dossier.dossier_code || '---'}
              </div>
              <div className="mb-3">
                <strong>موسسه:</strong> {dossier.institute.title || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌دهنده:</strong> {dossier.reporter?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌شونده:</strong> {dossier.reportee?.full_name || 'نامشخص'}
              </div>

              {/* Appeal Request Info */}
              <Alert variant="warning" className="mt-3">
                <i className="fe fe-alert-triangle me-2"></i>
                <strong>درخواست تجدیدنظر فعال:</strong>
                <br />
                درخواست تجدیدنظر توسط {dossier.revision_status === 'reportee_review' ? 'گزارش‌شونده' : 'گزارش‌کننده'}
                در تاریخ {dossier.revision_date} ثبت شده است.
              </Alert>
            </Card.Body>
          </Card>
        </Col>

        {/* Ministry Opinion Form */}
        <Col lg={8}>
          <Card>
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-award me-2"></i>
                صدور نظریه کارگروه وزارتی
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <Alert variant="primary">
                <i className="fe fe-info me-2"></i>
                <strong>صدور نظریه کارگروه وزارتی</strong>
                <br />
                در این مرحله، کارگروه وزارتی نسبت به تجدیدنظر درخواست شده، رای خود را به کارگروه موسسه اعلام و ارسال
                می‌کند.
              </Alert>

              <Form onSubmit={handleSubmit}>
                {/* Review Date */}
                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>
                        <strong>تاریخ جلسه تجدیدنظر:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <DatePicker
                        value={formData.reviewDate ? new Date(formData.reviewDate) : null}
                        calendar={persian}
                        locale={persian_fa}
                        calendarPosition="bottom-right"
                        containerClassName={'form-control'}
                        style={{
                          background: 'transparent',
                          width: '100%',
                          boxShadow: 'none!important',
                          outline: 'none',
                          border: 'none',
                        }}
                        onChange={(date) => {
                          const formattedDate = date ? formatDate(new Date(date)) : '';
                          handleInputChange('reviewDate', formattedDate);
                        }}
                        placeholder="تاریخ جلسه تجدیدنظر را انتخاب کنید"
                      />
                      <Form.Text className="text-muted">
                        تاریخ برگزاری جلسه کارگروه وزارتی برای بررسی درخواست تجدیدنظر
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Statements */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>اظهارات:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={6}
                        value={formData.statements}
                        onChange={(e) => handleInputChange('statements', e.target.value)}
                        placeholder="اظهارات و بحث‌های مطرح شده در جلسه کارگروه وزارتی را وارد کنید..."
                        required
                      />
                      <Form.Text className="text-muted">
                        خلاصه‌ای از بحث‌ها، نظرات و اظهارات مطرح شده در جلسه کارگروه وزارتی در خصوص درخواست تجدیدنظر.
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Ministry Opinion */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>نظریه کارگروه وزارتی:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={8}
                        value={formData.opinion}
                        onChange={(e) => handleInputChange('opinion', e.target.value)}
                        placeholder="نظریه و تصمیم کارگروه وزارتی در خصوص درخواست تجدیدنظر را وارد کنید..."
                        required
                      />
                      <Form.Text className="text-muted">
                        نظریه نهایی کارگروه وزارتی که به کارگروه موسسه ارسال می‌شود.
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Opinion Document Upload */}
                <Row className="mb-4">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>بارگذاری صورتجلسه:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        type="file"
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        onChange={handleFileUpload}
                        required={!formData.opinionDocument}
                        disabled={submitting}
                      />
                      <Form.Text className="text-muted">فرمت‌های مجاز: PDF, DOC, DOCX, JPG, PNG</Form.Text>

                      {formData.opinionDocument && (
                        <div className="mt-2">
                          <Alert variant="success" className="py-2 d-flex justify-content-between align-items-center">
                            <div>
                              <i className="fe fe-file me-2"></i>
                              فایل صورتجلسه با موفقیت آپلود شد: {formData.opinionDocument.name || 'فایل آپلود شده'}
                            </div>
                            <Button variant="outline-danger" size="sm" onClick={handleFileRemove}>
                              <i className="fe fe-trash-2"></i>
                              حذف
                            </Button>
                          </Alert>
                        </div>
                      )}
                    </Form.Group>
                  </Col>
                </Row>

                {/* Submit Button */}
                <div className="d-flex justify-content-end">
                  <Button type="submit" variant="success" disabled={submitting}>
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        در حال ثبت...
                      </>
                    ) : (
                      <>
                        <i className="fe fe-check me-2"></i>
                        ثبت نظریه و انتقال به مرحله بعد
                      </>
                    )}
                  </Button>
                </div>

                {/* Process Information */}
                <Alert variant="info" className="mt-3">
                  <i className="fe fe-info me-2"></i>
                  <strong>فرآیند بعدی:</strong> پس از تکمیل این بخش، نظریه کارگروه وزارتی به کارگروه موسسه ارسال شده و
                  کارگروه موسسه باید نظریه نهایی خود را با در نظر گیری نظر کارگروه وزارتی صادر کند.
                </Alert>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default MinistryOpinion;
